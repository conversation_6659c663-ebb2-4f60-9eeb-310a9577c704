import { useEffect, useState } from "react";

export const useToolbarVisibility = () => {
  const [isMainToolbarVisible, setIsMainToolbarVisible] = useState<boolean>(true);

  useEffect(() => {
    const handleToggleMainToolbar = (event: Event) => {
      const e = event as CustomEvent<{ visible: boolean }>;
      if (e.detail && typeof e.detail.visible === "boolean") setIsMainToolbarVisible(e.detail.visible);
    };
    window.addEventListener("toggleMainToolbar", handleToggleMainToolbar as EventListener);
    return () => {
      window.removeEventListener("toggleMainToolbar", handleToggleMainToolbar as EventListener);
    };
  }, []);

  return { isMainToolbarVisible, setIsMainToolbarVisible } as const;
};

