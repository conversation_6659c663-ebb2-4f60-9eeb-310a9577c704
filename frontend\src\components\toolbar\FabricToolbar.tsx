import React, { useState, useEffect } from "react";
import type { ImageToolbarProps, CalibrationData } from "@/models";
import { useFabricTools } from "@/hooks/useFabricTools";
import { Rnd } from "react-rnd";
import { Collapse } from "@blueprintjs/core";

import {
  createCalibrationSubmitHandler,
  createCalibrationCloseHandler,
  getCalibrationFromLocalStorage,
  clearCalibrationFromLocalStorage,
} from "@/lib/fabric/operations";
import ActionButtons from "./ActionButtons";
import AdjustmentsOverlay from "./AdjustmentsOverlay";
import CalibrationModal from "./CalibrationModal";
import CalibrationPrompt from "./CalibrationPrompt";

import {
  useToolbarVisibility,
  usePropertiesPanelController,
} from "@/hooks/useToolbarAndProperties";
import ToolGrid from "./ToolGrid";
import PropertiesPanel from "./PropertiesPanel";

const FabricToolbar: React.FC<ImageToolbarProps> = ({
  fabricCanvas,
  fabricConfigs,
  handlers,
  state,
  config = {},
  onShapeCreated,
}) => {
  const { disableGrayscale = false, disableGamma = false } = config;
  const [isCalibrationModalOpen, setCalibrationModalOpen] = useState(false);
  const [isCalibrationPromptOpen, setCalibrationPromptOpen] = useState(false);
  const [isAdjustmentsVisible, setIsAdjustmentsVisible] = useState(false);

  const [localCalibrationData, setLocalCalibrationData] = useState<CalibrationData>(() => {
    const localStorageData = getCalibrationFromLocalStorage();
    return localStorageData ?? fabricConfigs.calibrationData!;
  });
  useEffect(() => {
    if (fabricConfigs.calibrationData) {
      setLocalCalibrationData(fabricConfigs.calibrationData);
    }
    return () => {
      clearCalibrationFromLocalStorage();
    };
  }, [fabricConfigs.calibrationData]);

  const { activeMode, changeToolMode } = useFabricTools({
    fabricCanvas,
    cropData: state.cropData,
    onShapeCreated,
    onCrop: handlers.actions.handleCrop,
    disableUndoTracking: handlers.tracking.disableUndoTracking,
    enableUndoTracking: handlers.tracking.enableUndoTracking,
    showCalibrationModal: () => setCalibrationModalOpen(true),
    calibrationData: localCalibrationData || undefined,
    onCalibrationPrompt: () => setCalibrationPromptOpen(true),
  });
  const {
    isOpen: isPropertiesVisible,
    propertiesTool,
    onToolChange,
    close,
  } = usePropertiesPanelController(fabricCanvas as any, activeMode as unknown as any);

  // State for toolbar visibility controlled by AppToolbar
  const { isMainToolbarVisible } = useToolbarVisibility();

  useEffect(() => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    if (!(canvas as any).annotationColor) (canvas as any).annotationColor = "#ff0000";
  }, [fabricCanvas]);

  return (
    <>
      {isMainToolbarVisible && (
        <Rnd
          default={{ x: 0, y: window.innerHeight / 2 - 150, width: "auto", height: "auto" }}
          bounds=".viewer-panel"
          enableResizing={false}
          dragHandleClassName="bp5-dialog-header"
        >
          <div
            className="bp5-dialog"
            style={{
              margin: 0,
              background: "var(--pt-app-background-color, #fff)",
              border: "1px solid var(--pt-divider-black, #d9d9d9)",
            }}
          >
            <div
              className="bp5-dialog-header bp5-small"
              style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
            >
              <h4 className="bp5-heading" style={{ margin: 0 }}>
                Tools
              </h4>
            </div>
            <div className="bp5-dialog-body" style={{ padding: 6 }}>
              <div style={{ display: "flex", flexDirection: "column", gap: 4 }}>
                <ToolGrid
                  activeMode={activeMode}
                  cropData={state.cropData}
                  onToolSelect={(mode) => {
                    changeToolMode(mode);
                    onToolChange(mode as any);
                  }}
                  onCrop={handlers.actions.handleCrop}
                  grayscale={fabricConfigs.grayscale}
                  invert={fabricConfigs.invert}
                  disableGrayscale={disableGrayscale}
                  onRotate={() => {
                    close();
                    handlers.transform.handleRotate();
                  }}
                  onFlipHorizontal={() => {
                    close();
                    handlers.transform.handleFlipHorizontal();
                  }}
                  onFlipVertical={() => {
                    close();
                    handlers.transform.handleFlipVertical();
                  }}
                  onGrayscaleChange={(v) => {
                    close();
                    handlers.filter.handleGrayscaleChange(v);
                  }}
                  onInvertChange={(v) => {
                    close();
                    handlers.filter.handleInvertChange(v);
                  }}
                  onOpenAdjustments={() => setIsAdjustmentsVisible(true)}
                />
                <ActionButtons
                  canUndo={state.canUndo}
                  onUndo={handlers.actions.handleUndo}
                  onSave={handlers.actions.handleSave}
                  compact
                />
                <Collapse isOpen={isPropertiesVisible}>
                  <PropertiesPanel
                    open={isPropertiesVisible}
                    fabricCanvas={fabricCanvas as any}
                    propertiesTool={propertiesTool}
                    inline
                  />
                </Collapse>
              </div>
            </div>
          </div>
        </Rnd>
      )}
      <AdjustmentsOverlay
        isOpen={isAdjustmentsVisible}
        onClose={() => setIsAdjustmentsVisible(false)}
        brightness={fabricConfigs.brightness}
        contrast={fabricConfigs.contrast}
        sharpness={fabricConfigs.sharpness}
        gammaR={fabricConfigs.gammaR}
        gammaG={fabricConfigs.gammaG}
        gammaB={fabricConfigs.gammaB}
        disableGamma={disableGamma}
        onBrightnessChange={handlers.filter.handleBrightnessChange}
        onContrastChange={handlers.filter.handleContrastChange}
        onSharpnessChange={handlers.filter.handleSharpnessChange}
        onGammaRChange={handlers.filter.handleGammaRChange}
        onGammaGChange={handlers.filter.handleGammaGChange}
        onGammaBChange={handlers.filter.handleGammaBChange}
        canUndo={state.canUndo}
        onUndo={handlers.actions.handleUndo}
        onSave={handlers.actions.handleSave}
      />
      <CalibrationModal
        isOpen={isCalibrationModalOpen}
        onClose={createCalibrationCloseHandler(
          fabricCanvas?.current,
          setCalibrationModalOpen,
          handlers.tracking.disableUndoTracking,
          handlers.tracking.enableUndoTracking
        )}
        onSubmit={createCalibrationSubmitHandler(
          fabricCanvas?.current,
          () => {
            const newCalibrationData = getCalibrationFromLocalStorage();
            setLocalCalibrationData(newCalibrationData ?? fabricConfigs.calibrationData!);
            setCalibrationModalOpen(false);
            changeToolMode("measure");
          },
          handlers.tracking.disableUndoTracking,
          handlers.tracking.enableUndoTracking
        )}
      />
      <CalibrationPrompt
        isOpen={isCalibrationPromptOpen}
        onClose={() => setCalibrationPromptOpen(false)}
        onCalibrate={() => {
          setCalibrationPromptOpen(false);
          changeToolMode("calibrate");
        }}
      />
    </>
  );
};

export default FabricToolbar;
