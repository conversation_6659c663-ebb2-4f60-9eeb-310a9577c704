import React from "react";
import { Rnd } from "react-rnd";
import { Button, ControlGroup, HTMLSelect, Slider } from "@blueprintjs/core";
import type { CanvasRef } from "@/hooks/usePropertiesState";
import { usePropertiesState } from "@/hooks/usePropertiesState";

interface PropertiesPanelProps {
  open: boolean;
  fabricCanvas: CanvasRef;
  propertiesTool: string | null;
  inline?: boolean;
}

const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  open,
  fabricCanvas,
  propertiesTool,
  inline,
}) => {
  const {
    state,
    getCurrentStrokeWidth,
    getCurrentColor,
    getCurrentFontSize,
    getCurrentBold,
    getCurrentItalic,
    getCurrentUnderline,
    getCurrentFontFamily,
    getCurrentLineStyle,
    handleStrokeWidthChange,
    handleColorChange,
    handleFontSizeChange,
    handleBoldToggle,
    handleItalicToggle,
    handleUnderlineToggle,
    handleFontFamilyChange,
    handleLineStyleChange,
    handleMagnifierZoomChange,
    handleMagnifierRadiusChange,
  } = usePropertiesState(fabricCanvas);

  const controlsFor = (tool: string | null) => {
    const obj: any = fabricCanvas?.current?.getActiveObject?.();
    if (obj) {
      const type = obj.type;
      const name = obj.name || obj.customType;
      return {
        showMagnifier: false,
        showStrokeWidth: type !== "textbox" && name !== "cropRect" && name !== "protractor",
        showColor: true,
        showFontSize: type === "textbox",
        showBold: type === "textbox",
        showItalic: type === "textbox",
        showUnderline: type === "textbox",
        showFontFamily: type === "textbox",
        showLineStyle:
          type !== "textbox" &&
          name !== "cropRect" &&
          name !== "protractor" &&
          name !== "highlight",
      };
    }
    switch (tool) {
      case "magnifier":
        return {
          showMagnifier: true,
          showStrokeWidth: false,
          showColor: false,
          showFontSize: false,
          showBold: false,
          showItalic: false,
          showUnderline: false,
          showFontFamily: false,
          showLineStyle: false,
        };
      case "text":
        return {
          showMagnifier: false,
          showStrokeWidth: false,
          showColor: true,
          showFontSize: true,
          showBold: true,
          showItalic: true,
          showUnderline: true,
          showFontFamily: true,
          showLineStyle: false,
        };
      case "highlight":
        return {
          showMagnifier: false,
          showStrokeWidth: false,
          showColor: false,
          showFontSize: false,
          showBold: false,
          showItalic: false,
          showUnderline: false,
          showFontFamily: false,
          showLineStyle: false,
        };
      case "freehand":
      case "line":
      case "arrow":
      case "rect":
      case "circle":
      case "measure":
        return {
          showMagnifier: false,
          showStrokeWidth: true,
          showColor: true,
          showFontSize: false,
          showBold: false,
          showItalic: false,
          showUnderline: false,
          showFontFamily: false,
          showLineStyle: true,
        };
      default:
        return {
          showMagnifier: false,
          showStrokeWidth: false,
          showColor: false,
          showFontSize: false,
          showBold: false,
          showItalic: false,
          showUnderline: false,
          showFontFamily: false,
          showLineStyle: false,
        };
    }
  };

  if (!open) return null;

  const s = controlsFor(propertiesTool);

  const controls = (
    <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
      {s.showMagnifier && (
        <>
          <ControlGroup style={{ gap: 6, alignItems: "center" }}>
            <span className="bp5-text-muted" style={{ width: 60 }}>
              Zoom
            </span>
            <Slider
              min={1}
              max={5}
              stepSize={0.1}
              value={state.magnifierZoom}
              onChange={handleMagnifierZoomChange}
              labelRenderer={false}
            />
          </ControlGroup>
          <ControlGroup style={{ gap: 6, alignItems: "center" }}>
            <span className="bp5-text-muted" style={{ width: 60 }}>
              Size
            </span>
            <Slider
              min={20}
              max={300}
              stepSize={1}
              value={state.magnifierRadius}
              onChange={handleMagnifierRadiusChange}
              labelRenderer={false}
            />
          </ControlGroup>
        </>
      )}
      {s.showFontSize && (
        <ControlGroup style={{ gap: 6, alignItems: "center" }}>
          <span className="bp5-text-muted" style={{ width: 60 }}>
            Font
          </span>
          <Slider
            min={8}
            max={72}
            stepSize={1}
            value={getCurrentFontSize()}
            onChange={handleFontSizeChange}
            labelRenderer={false}
          />
        </ControlGroup>
      )}
      {s.showBold && (
        <Button
          icon="bold"
          active={getCurrentBold()}
          onClick={handleBoldToggle}
          className="bp5-minimal"
        />
      )}
      {s.showItalic && (
        <Button
          icon="italic"
          active={getCurrentItalic()}
          onClick={handleItalicToggle}
          className="bp5-minimal"
        />
      )}
      {s.showUnderline && (
        <Button
          icon="underline"
          active={getCurrentUnderline()}
          onClick={handleUnderlineToggle}
          className="bp5-minimal"
        />
      )}
      {s.showFontFamily && (
        <ControlGroup style={{ gap: 6, alignItems: "center" }}>
          <span className="bp5-text-muted" style={{ width: 60 }}>
            Family
          </span>
          <HTMLSelect
            value={getCurrentFontFamily()}
            onChange={(e) => handleFontFamilyChange(e.currentTarget.value)}
            options={["Arial", "Times New Roman", "Calibri"]}
            minimal
          />
        </ControlGroup>
      )}
      {s.showStrokeWidth && (
        <ControlGroup style={{ gap: 6, alignItems: "center" }}>
          <span className="bp5-text-muted" style={{ width: 60 }}>
            Stroke
          </span>
          <Slider
            min={1}
            max={10}
            stepSize={1}
            value={getCurrentStrokeWidth()}
            onChange={handleStrokeWidthChange}
            labelRenderer={false}
          />
        </ControlGroup>
      )}
      {s.showLineStyle && (
        <ControlGroup style={{ gap: 6, alignItems: "center" }}>
          <span className="bp5-text-muted" style={{ width: 60 }}>
            Line
          </span>
          <HTMLSelect
            value={getCurrentLineStyle()}
            onChange={(e) => handleLineStyleChange(e.currentTarget.value)}
            options={[
              { label: "Solid", value: "solid" },
              { label: "Dashed", value: "dashed" },
              { label: "Dotted", value: "dotted" },
            ]}
            minimal
          />
        </ControlGroup>
      )}
      {s.showColor && (
        <ControlGroup style={{ gap: 6, alignItems: "center" }}>
          <span className="bp5-text-muted" style={{ width: 60 }}>
            Color
          </span>
          <input
            type="color"
            value={getCurrentColor()}
            onChange={(e) => handleColorChange(e.target.value)}
          />
        </ControlGroup>
      )}
    </div>
  );

  if (inline) {
    return <div style={{ paddingTop: 6 }}>{controls}</div>;
  }

  return (
    <Rnd
      default={{ x: 220, y: window.innerHeight / 2 - 160, width: 260, height: 320 }}
      bounds=".viewer-panel"
      enableResizing={false}
      dragHandleClassName="bp5-dialog-header"
    >
      <div className="bp5-dialog" style={{ margin: 0 }}>
        <div
          className="bp5-dialog-header bp5-small"
          style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
        >
          <h4 className="bp5-heading" style={{ margin: 0 }}>
            Properties
          </h4>
        </div>
        <div className="bp5-dialog-body" style={{ padding: 8 }}>
          {controls}
        </div>
      </div>
    </Rnd>
  );
};

export default PropertiesPanel;
