import React from "react";
import { Rnd } from "react-rnd";
import { Button, ControlGroup, HTMLSelect, Slider } from "@blueprintjs/core";
import type { CanvasRef } from "@/hooks/usePropertiesState";
import { usePropertiesState } from "@/hooks/usePropertiesState";

interface PropertiesPanelProps {
  open: boolean;
  fabricCanvas: CanvasRef;
  propertiesTool: string | null;
  inline?: boolean;
}

const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  open,
  fabricCanvas,
  propertiesTool,
  inline,
}) => {
  const {
    state,
    getCurrentStrokeWidth,
    getCurrentColor,
    getCurrentFontSize,
    getCurrentBold,
    getCurrentItalic,
    getCurrentUnderline,
    getCurrentFontFamily,
    getCurrentLineStyle,
    handleStrokeWidthChange,
    handleColorChange,
    handleFontSizeChange,
    handleBoldToggle,
    handleItalicToggle,
    handleUnderlineToggle,
    handleFontFamilyChange,
    handleLineStyleChange,
    handleMagnifierZoomChange,
    handleMagnifierRadiusChange,
  } = usePropertiesState(fabricCanvas);

  const controlsFor = (tool: string | null) => {
    const obj: any = fabricCanvas?.current?.getActiveObject?.();
    if (obj) {
      const type = obj.type;
      const name = obj.name || obj.customType;
      return {
        showMagnifier: false,
        showStrokeWidth: type !== "textbox" && name !== "cropRect" && name !== "protractor",
        showColor: true,
        showFontSize: type === "textbox",
        showBold: type === "textbox",
        showItalic: type === "textbox",
        showUnderline: type === "textbox",
        showFontFamily: type === "textbox",
        showLineStyle:
          type !== "textbox" &&
          name !== "cropRect" &&
          name !== "protractor" &&
          name !== "highlight",
      };
    }
    switch (tool) {
      case "magnifier":
        return {
          showMagnifier: true,
          showStrokeWidth: false,
          showColor: false,
          showFontSize: false,
          showBold: false,
          showItalic: false,
          showUnderline: false,
          showFontFamily: false,
          showLineStyle: false,
        };
      case "text":
        return {
          showMagnifier: false,
          showStrokeWidth: false,
          showColor: true,
          showFontSize: true,
          showBold: true,
          showItalic: true,
          showUnderline: true,
          showFontFamily: true,
          showLineStyle: false,
        };
      case "highlight":
        return {
          showMagnifier: false,
          showStrokeWidth: false,
          showColor: false,
          showFontSize: false,
          showBold: false,
          showItalic: false,
          showUnderline: false,
          showFontFamily: false,
          showLineStyle: false,
        };
      case "freehand":
      case "line":
      case "arrow":
      case "rect":
      case "circle":
      case "measure":
        return {
          showMagnifier: false,
          showStrokeWidth: true,
          showColor: true,
          showFontSize: false,
          showBold: false,
          showItalic: false,
          showUnderline: false,
          showFontFamily: false,
          showLineStyle: true,
        };
      default:
        return {
          showMagnifier: false,
          showStrokeWidth: false,
          showColor: false,
          showFontSize: false,
          showBold: false,
          showItalic: false,
          showUnderline: false,
          showFontFamily: false,
          showLineStyle: false,
        };
    }
  };

  if (!open) return null;

  const s = controlsFor(propertiesTool);

  const controls = (
    <div style={{ display: "flex", flexDirection: "column", gap: 4 }}>
      {s.showMagnifier && (
        <>
          <ControlGroup className="bp5-small" style={{ gap: 4, alignItems: "center" }}>
            <span className="bp5-text-muted bp5-text-small" style={{ width: 44 }}>
              Zoom
            </span>
            <Slider
              className="bp5-small"
              min={1}
              max={5}
              stepSize={0.1}
              value={state.magnifierZoom}
              onChange={handleMagnifierZoomChange}
              labelRenderer={false}
            />
          </ControlGroup>
          <ControlGroup className="bp5-small" style={{ gap: 4, alignItems: "center" }}>
            <span className="bp5-text-muted bp5-text-small" style={{ width: 44 }}>
              Size
            </span>
            <Slider
              className="bp5-small"
              min={20}
              max={300}
              stepSize={1}
              value={state.magnifierRadius}
              onChange={handleMagnifierRadiusChange}
              labelRenderer={false}
            />
          </ControlGroup>
        </>
      )}
      {s.showFontSize && (
        <ControlGroup className="bp5-small" style={{ gap: 4, alignItems: "center" }}>
          <span className="bp5-text-muted bp5-text-small" style={{ width: 44 }}>
            Font
          </span>
          <Slider
            className="bp5-small"
            min={8}
            max={72}
            stepSize={1}
            value={getCurrentFontSize()}
            onChange={handleFontSizeChange}
            labelRenderer={false}
          />
        </ControlGroup>
      )}
      {(s.showBold || s.showItalic || s.showUnderline) && (
        <ControlGroup className="bp5-small" style={{ gap: 4, alignItems: "center" }}>
          {s.showBold && (
            <Button
              icon="bold"
              active={getCurrentBold()}
              onClick={handleBoldToggle}
              className="bp5-minimal bp5-small"
            />
          )}
          {s.showItalic && (
            <Button
              icon="italic"
              active={getCurrentItalic()}
              onClick={handleItalicToggle}
              className="bp5-minimal bp5-small"
            />
          )}
          {s.showUnderline && (
            <Button
              icon="underline"
              active={getCurrentUnderline()}
              onClick={handleUnderlineToggle}
              className="bp5-minimal bp5-small"
            />
          )}
        </ControlGroup>
      )}
      {s.showFontFamily && (
        <ControlGroup className="bp5-small" style={{ gap: 4, alignItems: "center" }}>
          <span className="bp5-text-muted bp5-text-small" style={{ width: 44 }}>
            Family
          </span>
          <HTMLSelect
            className="bp5-small"
            value={getCurrentFontFamily()}
            onChange={(e) => handleFontFamilyChange(e.currentTarget.value)}
            options={["Arial", "Times New Roman", "Calibri"]}
            minimal
          />
        </ControlGroup>
      )}
      {s.showStrokeWidth && (
        <ControlGroup className="bp5-small" style={{ gap: 4, alignItems: "center" }}>
          <span className="bp5-text-muted bp5-text-small" style={{ width: 44 }}>
            Stroke
          </span>
          <Slider
            className="bp5-small"
            min={1}
            max={10}
            stepSize={1}
            value={getCurrentStrokeWidth()}
            onChange={handleStrokeWidthChange}
            labelRenderer={false}
          />
        </ControlGroup>
      )}
      {s.showLineStyle && (
        <ControlGroup className="bp5-small" style={{ gap: 4, alignItems: "center" }}>
          <span className="bp5-text-muted bp5-text-small" style={{ width: 44 }}>
            Line
          </span>
          <HTMLSelect
            className="bp5-small"
            value={getCurrentLineStyle()}
            onChange={(e) => handleLineStyleChange(e.currentTarget.value)}
            options={[
              { label: "Solid", value: "solid" },
              { label: "Dashed", value: "dashed" },
              { label: "Dotted", value: "dotted" },
            ]}
            minimal
          />
        </ControlGroup>
      )}
      {s.showColor && (
        <ControlGroup className="bp5-small" style={{ gap: 4, alignItems: "center" }}>
          <span className="bp5-text-muted bp5-text-small" style={{ width: 44 }}>
            Color
          </span>
          <input
            type="color"
            value={getCurrentColor()}
            onChange={(e) => handleColorChange(e.target.value)}
            style={{ width: 24, height: 20, padding: 0 }}
          />
        </ControlGroup>
      )}
    </div>
  );

  if (inline) {
    return <div style={{ paddingTop: 2 }}>{controls}</div>;
  }

  return (
    <Rnd
      default={{ x: 220, y: window.innerHeight / 2 - 160, width: 220, height: 240 }}
      bounds=".viewer-panel"
      enableResizing={false}
      dragHandleClassName="bp5-dialog-header"
    >
      <div className="bp5-dialog" style={{ margin: 0 }}>
        <div
          className="bp5-dialog-header bp5-small"
          style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
        >
          <h4 className="bp5-heading" style={{ margin: 0 }}>
            Properties
          </h4>
        </div>
        <div className="bp5-dialog-body" style={{ padding: 6 }}>
          {controls}
        </div>
      </div>
    </Rnd>
  );
};

export default PropertiesPanel;
