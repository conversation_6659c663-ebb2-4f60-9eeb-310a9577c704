import { useEffect, useState } from "react";

export interface PropertiesPanelController {
  isOpen: boolean;
  propertiesTool: string | null;
  onToolChange: (mode: string) => void;
  close: () => void;
}

const toolsWithProperties: readonly string[] = [
  "text",
  "freehand",
  "line",
  "arrow",
  "rect",
  "circle",
  "measure",
  "highlight",
  "magnifier",
] as const;

export const usePropertiesPanelController = (
  fabricCanvas: React.MutableRefObject<fabric.Canvas | null>,
  activeMode: string | null
): PropertiesPanelController => {
  const [isOpen, setOpen] = useState(false);
  const [propertiesTool, setPropertiesTool] = useState<string | null>(null);

  const hasSelection = () => Boolean(fabricCanvas.current?.getActiveObject());

  const onToolChange = (mode: string) => {
    if (toolsWithProperties.includes(mode)) {
      setOpen(true);
      setPropertiesTool(hasSelection() ? mode : mode);
    } else {
      setOpen(false);
      setPropertiesTool(null);
    }
  };

  useEffect(() => {
    const canvas = fabricCanvas.current;
    if (!canvas) return;

    const handleSelCreated = () => {
      if (!activeMode) return;
      if (toolsWithProperties.includes(activeMode)) {
        setOpen(true);
        setPropertiesTool(activeMode);
      }
    };
    const handleSelUpdated = handleSelCreated;
    const handleSelCleared = () => {
      if (!activeMode) return;
      if (toolsWithProperties.includes(activeMode)) {
        setOpen(true);
        setPropertiesTool(activeMode);
      } else {
        setOpen(false);
        setPropertiesTool(null);
      }
    };

    canvas.on("selection:created", handleSelCreated);
    canvas.on("selection:updated", handleSelUpdated);
    canvas.on("selection:cleared", handleSelCleared);

    return () => {
      canvas.off("selection:created", handleSelCreated);
      canvas.off("selection:updated", handleSelUpdated);
      canvas.off("selection:cleared", handleSelCleared);
    };
  }, [fabricCanvas, activeMode]);

  const close = () => {
    setOpen(false);
    setPropertiesTool(null);
  };

  return { isOpen, propertiesTool, onToolChange, close } as const;
};
