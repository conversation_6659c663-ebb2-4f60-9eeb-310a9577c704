import { useEffect, useState, useMemo } from "react";
import { getAllDefaults } from "@/lib/fabric/utils";
import {
  getObjectColor,
  getObjectStrokeWidth,
  getObjectFontSize,
  getObjectFontWeight,
  getObjectFontStyle,
  getObjectUnderline,
  getObjectFontFamily,
  getObjectLineStyle,
  applyStrokeWidthContextAware,
  applyColorContextAware,
  applyFontSizeContextAware,
  applyFontWeightContextAware,
  applyFontStyleContextAware,
  applyUnderlineContextAware,
  applyFontFamilyContextAware,
  applyLineStyleContextAware,
} from "@/lib/fabric/operations";
import {
  updateMagnifierZoom as setCanvasMagnifierZoom,
  updateMagnifierRadius as setCanvasMagnifierRadius,
} from "@/lib/fabric/operations/magnifier";

export type CanvasRef = React.MutableRefObject<any | null>;

export interface PropertiesState {
  magnifierZoom: number;
  magnifierRadius: number;
  fontSize: number;
  isBold: boolean;
  isItalic: boolean;
  isUnderline: boolean;
  strokeWidth: number;
  annotationColor: string;
  fontFamily: string;
  lineStyle: string;
}

export interface PropertiesPanelController {
  isOpen: boolean;
  propertiesTool: string | null;
  onToolChange: (mode: string) => void;
  close: () => void;
}

const toolsWithProperties: readonly string[] = [
  "text",
  "freehand",
  "line",
  "arrow",
  "rect",
  "circle",
  "measure",
  "highlight",
  "magnifier",
] as const;

export const useToolbarVisibility = () => {
  const [isMainToolbarVisible, setIsMainToolbarVisible] = useState<boolean>(true);

  useEffect(() => {
    const handleToggleMainToolbar = (event: Event) => {
      const e = event as CustomEvent<{ visible: boolean }>;
      if (e.detail && typeof e.detail.visible === "boolean") setIsMainToolbarVisible(e.detail.visible);
    };
    window.addEventListener("toggleMainToolbar", handleToggleMainToolbar as EventListener);
    return () => {
      window.removeEventListener("toggleMainToolbar", handleToggleMainToolbar as EventListener);
    };
  }, []);

  return { isMainToolbarVisible, setIsMainToolbarVisible } as const;
};

export const usePropertiesPanelController = (
  fabricCanvas: React.MutableRefObject<fabric.Canvas | null>,
  activeMode: string | null
): PropertiesPanelController => {
  const [isOpen, setOpen] = useState(false);
  const [propertiesTool, setPropertiesTool] = useState<string | null>(null);

  const hasSelection = () => Boolean(fabricCanvas.current?.getActiveObject());

  const onToolChange = (mode: string) => {
    if (toolsWithProperties.includes(mode)) {
      setOpen(true);
      setPropertiesTool(hasSelection() ? mode : mode);
    } else {
      setOpen(false);
      setPropertiesTool(null);
    }
  };

  useEffect(() => {
    const canvas = fabricCanvas.current;
    if (!canvas) return;

    const handleSelCreated = () => {
      if (!activeMode) return;
      if (toolsWithProperties.includes(activeMode)) {
        setOpen(true);
        setPropertiesTool(activeMode);
      }
    };
    const handleSelUpdated = handleSelCreated;
    const handleSelCleared = () => {
      if (!activeMode) return;
      if (toolsWithProperties.includes(activeMode)) {
        setOpen(true);
        setPropertiesTool(activeMode);
      } else {
        setOpen(false);
        setPropertiesTool(null);
      }
    };

    canvas.on("selection:created", handleSelCreated);
    canvas.on("selection:updated", handleSelUpdated);
    canvas.on("selection:cleared", handleSelCleared);

    return () => {
      canvas.off("selection:created", handleSelCreated);
      canvas.off("selection:updated", handleSelUpdated);
      canvas.off("selection:cleared", handleSelCleared);
    };
  }, [fabricCanvas, activeMode]);

  const close = () => {
    setOpen(false);
    setPropertiesTool(null);
  };

  return { isOpen, propertiesTool, onToolChange, close } as const;
};

export const usePropertiesState = (fabricCanvas: CanvasRef) => {
  const defaults = useMemo(() => getAllDefaults(), []);
  const [state, setState] = useState<PropertiesState>({ ...defaults });

  const selectedObject = () => fabricCanvas?.current?.getActiveObject();

  const getCurrentStrokeWidth = () => {
    const w = getObjectStrokeWidth(selectedObject());
    return w !== undefined ? w : state.strokeWidth;
  };
  const getCurrentColor = () => {
    const c = getObjectColor(selectedObject());
    return c !== undefined ? c : state.annotationColor;
  };
  const getCurrentFontSize = () => {
    const fs = getObjectFontSize(selectedObject());
    return fs !== undefined ? fs : state.fontSize;
  };
  const getCurrentBold = () => {
    const b = getObjectFontWeight(selectedObject());
    return b !== undefined ? b : state.isBold;
  };
  const getCurrentItalic = () => {
    const it = getObjectFontStyle(selectedObject());
    return it !== undefined ? it : state.isItalic;
  };
  const getCurrentUnderline = () => {
    const u = getObjectUnderline(selectedObject());
    return u !== undefined ? u : state.isUnderline;
  };
  const getCurrentFontFamily = () => {
    const f = getObjectFontFamily(selectedObject());
    return f !== undefined ? f : state.fontFamily;
  };
  const getCurrentLineStyle = () => {
    const ls = getObjectLineStyle(selectedObject());
    return ls !== undefined ? ls : state.lineStyle;
  };

  const handleStrokeWidthChange = (width: number) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    applyStrokeWidthContextAware(canvas, width);
    setState((p) => ({ ...p, strokeWidth: width }));
  };
  const handleColorChange = (color: string) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    applyColorContextAware(canvas, color);
    setState((p) => ({ ...p, annotationColor: color }));
  };
  const handleFontSizeChange = (size: number) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    applyFontSizeContextAware(canvas, size);
    setState((p) => ({ ...p, fontSize: size }));
  };
  const handleBoldToggle = () => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    const next = !getCurrentBold();
    applyFontWeightContextAware(canvas, next);
    setState((p) => ({ ...p, isBold: next }));
  };
  const handleItalicToggle = () => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    const next = !getCurrentItalic();
    applyFontStyleContextAware(canvas, next);
    setState((p) => ({ ...p, isItalic: next }));
  };
  const handleUnderlineToggle = () => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    const next = !getCurrentUnderline();
    applyUnderlineContextAware(canvas, next);
    setState((p) => ({ ...p, isUnderline: next }));
  };
  const handleFontFamilyChange = (fontFamily: string) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    applyFontFamilyContextAware(canvas, fontFamily);
    setState((p) => ({ ...p, fontFamily }));
  };
  const handleLineStyleChange = (lineStyle: string) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    applyLineStyleContextAware(canvas, lineStyle);
    setState((p) => ({ ...p, lineStyle }));
  };
  const handleMagnifierZoomChange = (zoom: number) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    setCanvasMagnifierZoom(canvas, zoom);
    setState((p) => ({ ...p, magnifierZoom: zoom }));
  };
  const handleMagnifierRadiusChange = (radius: number) => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    setCanvasMagnifierRadius(canvas, radius);
    setState((p) => ({ ...p, magnifierRadius: radius }));
  };

  return {
    state,
    getCurrentStrokeWidth,
    getCurrentColor,
    getCurrentFontSize,
    getCurrentBold,
    getCurrentItalic,
    getCurrentUnderline,
    getCurrentFontFamily,
    getCurrentLineStyle,
    handleStrokeWidthChange,
    handleColorChange,
    handleFontSizeChange,
    handleBoldToggle,
    handleItalicToggle,
    handleUnderlineToggle,
    handleFontFamilyChange,
    handleLineStyleChange,
    handleMagnifierZoomChange,
    handleMagnifierRadiusChange,
  };
};
